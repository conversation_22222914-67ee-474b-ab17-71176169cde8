# Nouvelles Fonctionnalités - Extension Bullets

## Fonctionnalités Ajoutées

### 1. Sélection du Modèle Gemini
- **gemini-2.5-flash-lite** : Rapide et économique (par défaut)
- **gemini-2.5-flash** : <PERSON><PERSON>lib<PERSON>
- **gemini-2.5-pro** : Plus puissant

### 2. Configuration de la Langue
- **Français** : Réponses en français (par défaut)
- **English** : Réponses en anglais
- **Personnalisée** : Spécifiez votre propre langue (ex: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 日本語)

### 3. Prompts Personnalisés
- **Prompt de résumé** : Personnalisez la façon dont les textes sont résumés
- **Prompt de contexte** : Personnalisez les informations de contexte fournies
- **Prompt de génération de titre** : Personnalisez la génération des titres

## Comment Utiliser

### Accéder aux Paramètres
1. C<PERSON>z sur l'icône de l'extension Bullets
2. C<PERSON>z sur le bouton "⚙️ Paramètres"
3. Une nouvelle page s'ouvre avec tous les paramètres

### Configurer les Paramètres
1. **Modèle Gemini** : Sélectionnez le modèle souhaité dans la liste déroulante
2. **Langue** : Choisissez la langue des réponses
   - Si vous sélectionnez "Personnalisée", un champ apparaîtra pour saisir la langue
3. **Prompts** : Modifiez les prompts selon vos besoins
   - Utilisez `{TEXT}` comme placeholder pour le texte à traiter
4. Cliquez sur "Sauvegarder les paramètres"

### Fonctionnalités Avancées
- **Réinitialiser par défaut** : Remet tous les paramètres aux valeurs par défaut
- **Exporter la configuration** : Sauvegarde vos paramètres dans un fichier JSON
- **Importer la configuration** : Charge des paramètres depuis un fichier JSON

## Test des Fonctionnalités

### Test 1 : Changement de Modèle
1. Allez dans les paramètres
2. Changez le modèle de "gemini-2.5-flash-lite" à "gemini-2.5-pro"
3. Sauvegardez
4. Testez un résumé sur une page web
5. Vérifiez que le nouveau modèle est utilisé (dans les logs de la console)

### Test 2 : Changement de Langue
1. Allez dans les paramètres
2. Changez la langue de "Français" à "English"
3. Sauvegardez
4. Testez un résumé sur une page web
5. Vérifiez que la réponse est en anglais

### Test 3 : Langue Personnalisée
1. Allez dans les paramètres
2. Sélectionnez "Personnalisée"
3. Entrez "Español" dans le champ qui apparaît
4. Sauvegardez
5. Testez un résumé - la réponse devrait être en espagnol

### Test 4 : Prompt Personnalisé
1. Allez dans les paramètres
2. Modifiez le prompt de résumé pour qu'il soit plus court
3. Sauvegardez
4. Testez un résumé et vérifiez que le style a changé

## Vérification Technique

### Console du Navigateur
Ouvrez les outils de développement (F12) et vérifiez :
- Onglet "Console" : Pas d'erreurs JavaScript
- Onglet "Application" > "Storage" > "Local Storage" : Vérifiez que `bulletsSettings` est sauvegardé

### Logs Background Script
1. Allez dans `chrome://extensions/`
2. Activez le "Mode développeur"
3. Cliquez sur "Inspecter les vues" > "worker de service"
4. Vérifiez les logs lors de l'utilisation de l'extension

## Dépannage

### Problèmes Courants
1. **Les paramètres ne se sauvegardent pas** : Vérifiez les permissions de stockage
2. **La langue ne change pas** : Videz le cache et rechargez l'extension
3. **Les prompts personnalisés ne fonctionnent pas** : Vérifiez que `{TEXT}` est présent dans le prompt

### Réinitialisation Complète
Si quelque chose ne fonctionne pas :
1. Allez dans les paramètres
2. Cliquez sur "Réinitialiser par défaut"
3. Rechargez l'extension dans `chrome://extensions/`
