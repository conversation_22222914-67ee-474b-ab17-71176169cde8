/* ===================================================
   GLOBAL POPUP STYLES
=================================================== */
.bullets-ext-popup {
    --min-width: 300px;
    --max-width: 800px;
    --min-height: 200px;
    --max-height: 800px;
  
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 430px;
    height: 600px;
    min-width: var(--min-width);
    max-width: var(--max-width);
    min-height: var(--min-height);
    max-height: var(--max-height);
    background: rgba(255, 255, 255, 0.85) !important;
    backdrop-filter: blur(30px) saturate(200%);
    -webkit-backdrop-filter: blur(30px) saturate(200%);
    padding: 20px 20px 15px;
    border-radius: 10px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
                inset 0 0 0 1px rgba(255, 255, 255, 0.2);
    z-index: 2147483647;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    opacity: 0;
    animation: bullets-ext-fadeIn 0.3s ease-out forwards;
    display: flex;
    flex-direction: column;
    transition: box-shadow 0.2s ease, transform 0.2s ease;
  }
  
  @keyframes bullets-ext-fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* ===================================================
     TITLE BAR & PIN ICON
  =================================================== */
  .bullets-ext-popup .popup-title {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
    padding: 4px 4px;
    margin-bottom: 6px;
    min-height: 34px;
    color: #339133;
    cursor: move;
    user-select: none;
    background-color: transparent !important;
  }
  
  .bullets-ext-popup .popup-title .popup-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
    position: relative;
    border-radius: 50%;
    background-color: transparent;
  }
  
  .bullets-ext-popup .popup-title .title-text {
    flex: 1;
    margin: 0 8px;
    transition: opacity 0.3s ease, transform 0.3s ease;
    font-size: 15px;
    font-weight: 600;
    color: #339133;
    margin-bottom: 0px;
    width: 100%;
    background-color: transparent !important;
  }
  
  .bullets-ext-popup .popup-title .pin-icon {
    width: 24px;
    height: 24px;
    position: relative;
    border-radius: 50%;
    cursor: pointer;
    transition: transform 0.2s ease, background-color 0.2s ease;
  }
  
  /* Pin icon states */
  .bullets-ext-popup .popup-title .pin-icon::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background-color: rgba(51, 145, 51, 0.51);
    border-radius: 50%;
    z-index: 0;
  }
  .bullets-ext-popup .popup-title .pin-icon[title="Pin"]::after {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M16,12V4H17V2H7V4H8V12L6,14V16H11.2V22H12.8V16H18V14L16,12Z'/%3E%3C/svg%3E");
    background-size: 14px 14px;
    background-position: center;
    background-repeat: no-repeat;
    z-index: 1;
  }
  .bullets-ext-popup .popup-title .pin-icon[title="Close"]::after {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z'/%3E%3C/svg%3E");
    background-size: 14px 14px;
    background-position: center;
    background-repeat: no-repeat;
    z-index: 1;
  }
  
  /* ===================================================
     CONTENT AREA (Tabbed Interface)
  =================================================== */
  .bullets-ext-popup .popup-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: transparent !important;
    overflow: hidden;
  }
  
  .bullets-ext-popup .popup-content .tab-header {
    display: flex;
    border-bottom: 1px solid rgba(51, 145, 51, 0.1);
  }
  
  .bullets-ext-popup .popup-content .tab-button {
    flex: 1;
    padding: 8px;
    background: transparent;
    border: none;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .bullets-ext-popup .popup-content .tab-button.active {
    background: rgba(51, 145, 51, 0.1);
    font-weight: bold;
  }
  
  .bullets-ext-popup .popup-content .tab-panels {
    flex: 1;
    overflow-y: auto;
  }
  
  /* Each tab panel; default hidden except active */
  .bullets-ext-popup .popup-content .tab-panel {
    display: none;
  }
  
  .bullets-ext-popup .popup-content .tab-panel.active {
    display: block;
  }
  
  .bullets-ext-popup .popup-content .tab-panel.summary.active {
    margin: 0;
    padding: 0;
    background: none;
  }
  
  /* ===================================================
     ACTIONS SECTION (Buttons, Time Saved, etc.)
  =================================================== */
  .bullets-ext-popup .popup-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;
    flex-wrap: wrap;
  }
  
  .bullets-ext-popup .copy-button,
  .bullets-ext-popup .link-button,
  .bullets-ext-popup .save-button {
    background: linear-gradient(135deg, #339133, #2b7a2b);
    color: #fff;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0.3px;
    box-shadow: 0 2px 4px rgba(51, 145, 51, 0.2),
                0 1px 2px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 70px;
    height: 34px;
    cursor: pointer;
  }
  
  .bullets-ext-popup .copy-button:hover,
  .bullets-ext-popup .link-button:hover,
  .bullets-ext-popup .save-button:hover {
    background: linear-gradient(135deg, #2b7a2b, #236023);
    transform: translateY(-1px);
  }
  
  .bullets-ext-popup .copy-button:active,
  .bullets-ext-popup .link-button:active,
  .bullets-ext-popup .save-button:active {
    transform: translateY(1px);
  }
  
  /* Time saved "pill" styling */
  .bullets-ext-popup .time-saved {
    margin-left: auto;
    font-size: 10px;
    background: rgba(232, 232, 232, 0.8);
    color: #666;
    padding: 4px 12px;
    border-radius: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
    transition: opacity 0.3s ease;
    /* Center the time saved pill vertically in its container */
    display: flex;
    align-items: center;
  }
  .bullets-ext-popup .time-saved.updating {
    opacity: 0;
  }
  
  /* ===================================================
     RESIZE & DRAG HANDLES
  =================================================== */
  .bullets-ext-popup .resize-handle-left {
    position: absolute;
    left: 0;
    top: 0;
    width: 4px;
    height: 100%;
    cursor: ew-resize;
    background: transparent;
  }
  .bullets-ext-popup .resize-handle-top {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 4px;
    cursor: ns-resize;
    background: transparent;
  }
  .bullets-ext-popup .resize-handle-corner {
    position: absolute;
    left: 0;
    top: 0;
    width: 15px;
    height: 15px;
    cursor: nw-resize;
    background: linear-gradient(315deg, transparent 50%, #339133 50%);
    transition: opacity 0.2s ease;
  }
  .bullets-ext-popup:hover .resize-handle-corner {
    opacity: 1;
  }
  
  /* ===================================================
     SCROLLBAR STYLING (Content Area)
  =================================================== */
  .bullets-ext-popup .popup-content .tab-panels {
    scrollbar-width: thin;
    scrollbar-color: #339133 #f0f0f0;
  }
  .bullets-ext-popup .popup-content .tab-panels::-webkit-scrollbar {
    width: 8px;
  }
  .bullets-ext-popup .popup-content .tab-panels::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 4px;
  }
  .bullets-ext-popup .popup-content .tab-panels::-webkit-scrollbar-thumb {
    background: #339133;
    border-radius: 4px;
  }
  
  /* ===================================================
     LIST & TEXT STYLES (Within Content)
  =================================================== */
  .bullets-ext-popup .content h3 {
    font-size: 15px;
    font-weight: 600;
    color: #339133;
    padding: 8px 0;
    margin: 16px 0 12px;
    width: 100%;
    border-bottom: 2px solid rgba(51, 145, 51, 0.1);
    transition: border-bottom-color 0.2s ease;
    box-sizing: border-box;
  }
  .bullets-ext-popup .content h3:hover {
    border-bottom-color: rgba(51, 145, 51, 0.3);
  }
  .bullets-ext-popup .content ul {
    margin: 0 0 12px;
    padding-left: 0;
    list-style: none;
  }
  .bullets-ext-popup .content li {
    position: relative;
    margin: 4px 0;
    padding: 6px 8px 6px 28px;
    border-radius: 6px;
    transition: box-shadow 0.2s ease, transform 0.2s ease;
    background: rgba(51, 145, 51, 0.05);
    font-weight: 500;
    opacity: 0;
    animation: fadeInList 0.5s ease-in forwards;
  }
  .bullets-ext-popup .content li:hover {
    background: rgba(51, 145, 51, 0.1);
  }
  .bullets-ext-popup .content li::before {
    content: "";
    position: absolute;
    left: 8px;
    top: 12px;
    width: 6px;
    height: 6px;
    background: #339133;
    border-radius: 50%;
  }
  
  /* Hide default paragraph and line-break spacing */
  .bullets-ext-popup .content p,
  .bullets-ext-popup .content br {
    display: none;
  }
  
  /* ===================================================
     DARK MODE ADJUSTMENTS
  =================================================== */
  @media (prefers-color-scheme: dark) {
    .bullets-ext-popup {
      background: rgba(30, 30, 30, 0.85) !important;
      color: #ccc;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
    .bullets-ext-popup .popup-title {
      color: #ccc;
    }
    .bullets-ext-popup .popup-title .pin-icon {
      background-color: #4CAF50;
    }
    .bullets-ext-popup .copy-button,
    .bullets-ext-popup .link-button,
    .bullets-ext-popup .save-button {
      background: linear-gradient(135deg, #2b7a2b, #1e5e1e);
      box-shadow: 0 2px 4px rgba(0,0,0,0.2),
                  0 1px 2px rgba(0,0,0,0.1);
    }
    .bullets-ext-popup .copy-button:hover,
    .bullets-ext-popup .link-button:hover,
    .bullets-ext-popup .save-button:hover {
      background: linear-gradient(135deg, #236023, #184d18);
    }
    .bullets-ext-popup .time-saved {
      color: #ccc;
      background: rgba(232, 232, 232, 0.1);
    }
    .bullets-ext-popup .popup-content .tab-panels::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
    }
    .bullets-ext-popup .popup-content .tab-panels::-webkit-scrollbar-thumb {
      background: linear-gradient(to bottom, #4CAF50, #388E3C);
    }
    .bullets-ext-popup .content h3 {
      color: #fff;
      border-bottom-color: rgba(76, 175, 80, 0.1);
    }
    .bullets-ext-popup .content h3:hover {
      border-bottom-color: rgba(76, 175, 80, 0.3);
    }
    .bullets-ext-popup .font-size-button {
      background-color: #224a23;
    }
    .bullets-ext-popup .font-size-button:hover {
      background-color: #153816;
    }
  }
  
  /* ===================================================
     HOVER & ACTIVE STATES
  =================================================== */
  .bullets-ext-popup:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.25);
    transform: translateY(-1px);
  }
  .bullets-ext-popup .copy-button:active,
  .bullets-ext-popup .link-button:active,
  .bullets-ext-popup .save-button:active {
    transform: translateY(1px);
  }
  
  /* ===================================================
     MISCELLANEOUS OVERRIDES
  =================================================== */
  .bullets-ext-popup,
  .bullets-ext-popup * {
    transition: box-shadow 0.2s ease, transform 0.2s ease;
  }
  /* Allow list items and the streaming wrapper to animate */
  .bullets-ext-popup .content:not(.no-fade),
  .bullets-ext-popup .content:not(.no-fade) > :not(li):not(span) {
      opacity: 1 !important;
  }
  
  .fade-in {
    /* This animation will make the list items fade in from 0% to 100% opacity */
    animation: fadeInList 0.5s ease-in forwards;
  }
  
  @keyframes fadeInList {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  