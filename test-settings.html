<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test des Paramètres Bullets</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .test-result {
            background: #e8f5e8;
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
            border-left: 4px solid #4caf50;
        }
        .test-error {
            background: #ffe8e8;
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
            border-left: 4px solid #f44336;
        }
        button {
            background: #339133;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2b7a2b;
        }
        pre {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Test des Paramètres Bullets</h1>
    
    <div class="test-section">
        <h2>Test 1: Sauvegarde des Paramètres</h2>
        <button onclick="testSaveSettings()">Sauvegarder des paramètres de test</button>
        <div id="saveResult"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Chargement des Paramètres</h2>
        <button onclick="testLoadSettings()">Charger les paramètres</button>
        <div id="loadResult"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Génération de Prompts</h2>
        <button onclick="testPromptGeneration()">Tester la génération de prompts</button>
        <div id="promptResult"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 4: Configuration par Défaut</h2>
        <button onclick="testDefaultConfig()">Tester la configuration par défaut</button>
        <div id="defaultResult"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 5: Réinitialisation</h2>
        <button onclick="testReset()">Réinitialiser les paramètres</button>
        <div id="resetResult"></div>
    </div>

    <script>
        // Simuler l'API Chrome Storage pour les tests
        const mockStorage = {
            data: {},
            local: {
                get: function(keys, callback) {
                    const result = {};
                    if (Array.isArray(keys)) {
                        keys.forEach(key => {
                            if (this.data[key] !== undefined) {
                                result[key] = this.data[key];
                            }
                        });
                    } else if (typeof keys === 'string') {
                        if (this.data[keys] !== undefined) {
                            result[keys] = this.data[keys];
                        }
                    } else if (keys === null || keys === undefined) {
                        Object.assign(result, this.data);
                    }
                    callback(result);
                },
                set: function(items, callback) {
                    Object.assign(this.data, items);
                    if (callback) callback();
                }
            }
        };

        // Paramètres de test
        const testSettings = {
            model: 'gemini-2.5-pro',
            language: 'english',
            customLanguage: '',
            prompts: {
                summarize: 'Test summarize prompt with {TEXT}',
                context: 'Test context prompt with {TEXT}',
                title: 'Test title prompt with {TEXT}'
            }
        };

        function showResult(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${isError ? 'test-error' : 'test-result'}">${message}</div>`;
        }

        function testSaveSettings() {
            try {
                mockStorage.local.set({ bulletsSettings: testSettings }, () => {
                    showResult('saveResult', '✅ Paramètres sauvegardés avec succès!<br><pre>' + JSON.stringify(testSettings, null, 2) + '</pre>');
                });
            } catch (error) {
                showResult('saveResult', '❌ Erreur lors de la sauvegarde: ' + error.message, true);
            }
        }

        function testLoadSettings() {
            try {
                mockStorage.local.get(['bulletsSettings'], (data) => {
                    const settings = data.bulletsSettings;
                    if (settings) {
                        showResult('loadResult', '✅ Paramètres chargés avec succès!<br><pre>' + JSON.stringify(settings, null, 2) + '</pre>');
                    } else {
                        showResult('loadResult', '⚠️ Aucun paramètre trouvé (utilisation des paramètres par défaut)');
                    }
                });
            } catch (error) {
                showResult('loadResult', '❌ Erreur lors du chargement: ' + error.message, true);
            }
        }

        function testPromptGeneration() {
            try {
                mockStorage.local.get(['bulletsSettings'], (data) => {
                    const settings = data.bulletsSettings || {};
                    const prompts = settings.prompts || {};
                    
                    const testText = "Ceci est un texte de test";
                    const results = [];
                    
                    if (prompts.summarize) {
                        const summarizePrompt = prompts.summarize.replace('{TEXT}', testText);
                        results.push(`<strong>Prompt de résumé:</strong><br><pre>${summarizePrompt}</pre>`);
                    }
                    
                    if (prompts.context) {
                        const contextPrompt = prompts.context.replace('{TEXT}', testText);
                        results.push(`<strong>Prompt de contexte:</strong><br><pre>${contextPrompt}</pre>`);
                    }
                    
                    if (prompts.title) {
                        const titlePrompt = prompts.title.replace('{TEXT}', testText);
                        results.push(`<strong>Prompt de titre:</strong><br><pre>${titlePrompt}</pre>`);
                    }
                    
                    showResult('promptResult', '✅ Génération de prompts réussie!<br>' + results.join('<br>'));
                });
            } catch (error) {
                showResult('promptResult', '❌ Erreur lors de la génération: ' + error.message, true);
            }
        }

        function testDefaultConfig() {
            const defaultSettings = {
                model: 'gemini-2.5-flash-lite',
                language: 'french',
                customLanguage: '',
                prompts: {
                    summarize: 'Default summarize prompt...',
                    context: 'Default context prompt...',
                    title: 'Default title prompt...'
                }
            };
            
            showResult('defaultResult', '✅ Configuration par défaut:<br><pre>' + JSON.stringify(defaultSettings, null, 2) + '</pre>');
        }

        function testReset() {
            try {
                mockStorage.local.set({ bulletsSettings: null }, () => {
                    showResult('resetResult', '✅ Paramètres réinitialisés avec succès!');
                });
            } catch (error) {
                showResult('resetResult', '❌ Erreur lors de la réinitialisation: ' + error.message, true);
            }
        }

        // Test automatique au chargement
        window.onload = function() {
            console.log('Page de test chargée. Vous pouvez maintenant tester les fonctionnalités.');
        };
    </script>
</body>
</html>
