// settings.js

// Paramètres par défaut
const DEFAULT_SETTINGS = {
  model: 'gemini-2.5-flash-lite',
  language: 'french',
  customLanguage: '',
  prompts: {
    summarize: `You are an expert reporter tasked with creating clear, engaging, and informative outlines of various texts. Your goal is to capture the most important details while presenting them in a structured and easily digestible format.

Please follow these steps to create your summary:

1. Carefully read through the entire text and:
   a. Identify the key takeaways in the text.
   b. For each takeaway, identify the most important and interesting points.
   c. Present each point clearly and concisely, using analogies or relatable examples.
   d. Ensure you're capturing the most salient details from the original text.
   e. Check that each point can be expressed concisely in 1-2 lines (15-30 words).

Based on your analysis, create a structured outline summary following these rules:
   - The first section title is "Title: Key Takeaways".
   - The following sections go into more detail on each takeaway.
   - Start each section with "Title: " followed by title of the takeaway.
   - Use bullet points (•) for each key point under a title.
   - Keep points concise (1-2 lines) and engaging.
   - End each point with a period.
   - Do not use sub-bullets or nested points.
   - Do not use any extra formatting or special characters.

Your final output should follow this exact format:

Title: [Key takeaways]
• takeaway 1
• takeaway 2
...

Title: next takeaways
• [Point 1]
• [Point 2]

...

Remember to make your summary engaging and informative, capturing the most important details from the original text while tailoring it to the target audience. It is essential for you not to repeat yourself.
Here is the text you need to summarize:
{TEXT}`,

    context: `Provide background information about the topic, that's not in the article. If the event has a long timeline, explain the last significant events leading up to it. Be concise and to the point. Use bullet points, but no ** or sub-bullets.
Your final output should follow this exact format:

   - Start each section with "Title: " followed by title of the section.
   - Use bullet points (•) for each key point under a title.
   - Keep points concise (3-5 lines) and engaging.
   - End each point with a period.
   - Do not use sub-bullets or nested points.
   - Do not use any extra formatting or special characters.

Here is an example of the format you should follow:

Title: [Background information]
• background 1
• background 2
...
Title: [Approximate Timeline]
• Event 1
• Event 2
...

Here is the text you need to provide background information for:
{TEXT}`,

    title: `Create a short title (3-7 words) that captures the main topic.
Only output the title itself, with no prefix or formatting.

Text to summarize:
{TEXT}`
  }
};

// Charger les paramètres au démarrage
document.addEventListener('DOMContentLoaded', function() {
  loadSettings();
  setupEventListeners();
});

function setupEventListeners() {
  // Gestion du retour à la popup
  document.getElementById('backLink').addEventListener('click', function(e) {
    e.preventDefault();
    window.close();
  });

  // Gestion de la langue personnalisée
  document.getElementById('languageSelect').addEventListener('change', function() {
    const customInput = document.getElementById('customLanguageInput');
    if (this.value === 'custom') {
      customInput.style.display = 'block';
    } else {
      customInput.style.display = 'none';
    }
  });

  // Boutons d'action
  document.getElementById('saveSettings').addEventListener('click', saveSettings);
  document.getElementById('resetSettings').addEventListener('click', resetSettings);
  document.getElementById('exportSettings').addEventListener('click', exportSettings);
  document.getElementById('importSettings').addEventListener('click', function() {
    document.getElementById('importFile').click();
  });
  document.getElementById('importFile').addEventListener('change', importSettings);
}

function loadSettings() {
  chrome.storage.local.get(['bulletsSettings'], function(data) {
    const settings = data.bulletsSettings || DEFAULT_SETTINGS;
    
    // Charger le modèle
    document.getElementById('modelSelect').value = settings.model || DEFAULT_SETTINGS.model;
    
    // Charger la langue
    document.getElementById('languageSelect').value = settings.language || DEFAULT_SETTINGS.language;
    document.getElementById('customLanguage').value = settings.customLanguage || '';
    
    // Afficher le champ personnalisé si nécessaire
    if (settings.language === 'custom') {
      document.getElementById('customLanguageInput').style.display = 'block';
    }
    
    // Charger les prompts
    document.getElementById('summarizePrompt').value = settings.prompts?.summarize || DEFAULT_SETTINGS.prompts.summarize;
    document.getElementById('contextPrompt').value = settings.prompts?.context || DEFAULT_SETTINGS.prompts.context;
    document.getElementById('titlePrompt').value = settings.prompts?.title || DEFAULT_SETTINGS.prompts.title;
  });
}

function saveSettings() {
  const settings = {
    model: document.getElementById('modelSelect').value,
    language: document.getElementById('languageSelect').value,
    customLanguage: document.getElementById('customLanguage').value,
    prompts: {
      summarize: document.getElementById('summarizePrompt').value || DEFAULT_SETTINGS.prompts.summarize,
      context: document.getElementById('contextPrompt').value || DEFAULT_SETTINGS.prompts.context,
      title: document.getElementById('titlePrompt').value || DEFAULT_SETTINGS.prompts.title
    }
  };
  
  chrome.storage.local.set({ bulletsSettings: settings }, function() {
    // Invalider le cache de configuration dans background.js
    chrome.runtime.sendMessage({ action: 'clearConfigCache' });
    
    // Afficher le message de succès
    const successMessage = document.getElementById('successMessage');
    successMessage.style.display = 'block';
    setTimeout(() => {
      successMessage.style.display = 'none';
    }, 3000);
  });
}

function resetSettings() {
  if (confirm('Êtes-vous sûr de vouloir réinitialiser tous les paramètres par défaut ?')) {
    chrome.storage.local.set({ bulletsSettings: DEFAULT_SETTINGS }, function() {
      loadSettings();
      chrome.runtime.sendMessage({ action: 'clearConfigCache' });
      
      const successMessage = document.getElementById('successMessage');
      successMessage.textContent = 'Paramètres réinitialisés avec succès !';
      successMessage.style.display = 'block';
      setTimeout(() => {
        successMessage.style.display = 'none';
        successMessage.textContent = 'Paramètres sauvegardés avec succès !';
      }, 3000);
    });
  }
}

function exportSettings() {
  chrome.storage.local.get(['bulletsSettings'], function(data) {
    const settings = data.bulletsSettings || DEFAULT_SETTINGS;
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = 'bullets-settings.json';
    link.click();
  });
}

function importSettings(event) {
  const file = event.target.files[0];
  if (!file) return;
  
  const reader = new FileReader();
  reader.onload = function(e) {
    try {
      const settings = JSON.parse(e.target.result);
      
      // Valider la structure des paramètres
      if (validateSettings(settings)) {
        chrome.storage.local.set({ bulletsSettings: settings }, function() {
          loadSettings();
          chrome.runtime.sendMessage({ action: 'clearConfigCache' });
          
          const successMessage = document.getElementById('successMessage');
          successMessage.textContent = 'Configuration importée avec succès !';
          successMessage.style.display = 'block';
          setTimeout(() => {
            successMessage.style.display = 'none';
            successMessage.textContent = 'Paramètres sauvegardés avec succès !';
          }, 3000);
        });
      } else {
        alert('Fichier de configuration invalide.');
      }
    } catch (error) {
      alert('Erreur lors de l\'importation : fichier JSON invalide.');
    }
  };
  reader.readAsText(file);
  
  // Reset le champ file
  event.target.value = '';
}

function validateSettings(settings) {
  return settings && 
         typeof settings.model === 'string' &&
         typeof settings.language === 'string' &&
         settings.prompts &&
         typeof settings.prompts.summarize === 'string' &&
         typeof settings.prompts.context === 'string' &&
         typeof settings.prompts.title === 'string';
}
